"""
FastAPI main application for the chat system with booking and dashboard
"""
import os
import uuid
from datetime import datetime
from typing import Dict, Any, List, Optional
from contextlib import asynccontextmanager

from fastapi import FastAPI, HTTPException, Depends, status
from fastapi.middleware.cors import CORSMiddleware
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
from pydantic import BaseModel

# Local imports
from config.database import db_config, initialize_collections, create_sample_time_slots
from services.chat_service import get_chat_service
from routes import auth, chat, booking, dashboard

# Security
security = HTTPBearer()

@asynccontextmanager
async def lifespan(app: FastAPI):
    """Application lifespan events"""
    # Startup
    print("🚀 Starting Chat System API...")
    
    # Initialize database
    if await db_config.connect():
        print("✅ Database connected successfully")
        await initialize_collections()
        await create_sample_time_slots()
        print("✅ Database initialized")
    else:
        print("❌ Failed to connect to database")
        raise Exception("Database connection failed")
    
    # Initialize chat service
    try:
        chat_service = get_chat_service()
        print("✅ Chat service initialized")
    except Exception as e:
        print(f"❌ Failed to initialize chat service: {e}")
        raise
    
    yield
    
    # Shutdown
    print("🛑 Shutting down Chat System API...")
    db_config.close_connection()
    print("✅ Database connection closed")

# Create FastAPI app
app = FastAPI(
    title="Chat System API",
    description="Advanced chat system with booking, analytics, and dashboard",
    version="1.0.0",
    lifespan=lifespan
)

# CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["http://localhost:3000", "http://localhost:5173"],  # React dev servers
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Request/Response models
class ChatRequest(BaseModel):
    message: str
    session_id: Optional[str] = None
    user_id: Optional[str] = None

class ChatResponse(BaseModel):
    response: str
    session_id: str
    user_message_id: str
    assistant_message_id: str
    response_time_ms: int
    user_analytics: Optional[Dict[str, Any]] = None
    assistant_analytics: Optional[Dict[str, Any]] = None

class HealthResponse(BaseModel):
    status: str
    timestamp: str
    version: str
    database_connected: bool

# Health check endpoint
@app.get("/health", response_model=HealthResponse)
async def health_check():
    """Health check endpoint"""
    try:
        # Test database connection
        db = db_config.get_database()
        db.command("ping")
        db_connected = True
    except:
        db_connected = False
    
    return HealthResponse(
        status="healthy" if db_connected else "unhealthy",
        timestamp=datetime.utcnow().isoformat(),
        version="1.0.0",
        database_connected=db_connected
    )

# Chat endpoint
@app.post("/api/chat", response_model=ChatResponse)
async def chat_endpoint(request: ChatRequest):
    """Main chat endpoint with analytics"""
    try:
        # Generate session ID if not provided
        if not request.session_id:
            request.session_id = str(uuid.uuid4())
        
        # Get chat service
        chat_service = get_chat_service()
        
        # Process message
        result = await chat_service.process_message(
            content=request.message,
            session_id=request.session_id,
            user_id=request.user_id
        )
        
        if "error" in result:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=result["error"]
            )
        
        return ChatResponse(**result)
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Chat processing failed: {str(e)}"
        )

# Conversation history endpoint
@app.get("/api/chat/history/{session_id}")
async def get_conversation_history(session_id: str, limit: int = 50):
    """Get conversation history for a session"""
    try:
        chat_service = get_chat_service()
        history = chat_service.get_conversation_history(session_id, limit)
        
        return {
            "session_id": session_id,
            "messages": history,
            "total_messages": len(history)
        }
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to retrieve conversation history: {str(e)}"
        )

# User analytics endpoint
@app.get("/api/analytics/user/{user_id}")
async def get_user_analytics(user_id: str):
    """Get analytics for a specific user"""
    try:
        chat_service = get_chat_service()
        analytics = chat_service.get_user_analytics(user_id)
        
        return {
            "user_id": user_id,
            "analytics": analytics
        }
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to retrieve user analytics: {str(e)}"
        )

# Include routers
app.include_router(auth.router, prefix="/api/auth", tags=["Authentication"])
app.include_router(chat.router, prefix="/api/chat", tags=["Chat"])
app.include_router(booking.router, prefix="/api/booking", tags=["Booking"])
app.include_router(dashboard.router, prefix="/api/dashboard", tags=["Dashboard"])

# Root endpoint
@app.get("/")
async def root():
    """Root endpoint"""
    return {
        "message": "Chat System API",
        "version": "1.0.0",
        "docs": "/docs",
        "health": "/health"
    }

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(
        "main:app",
        host="0.0.0.0",
        port=8000,
        reload=True,
        log_level="info"
    )
