#!/usr/bin/env python3
"""
Quick Database Check Script
This script quickly checks if the database is accessible and shows basic info
"""

import asyncio
import sys
from pathlib import Path

# Add the backend directory to Python path
backend_dir = Path(__file__).parent
sys.path.insert(0, str(backend_dir))

from config.database import db_config, get_db, get_collection, COLLECTIONS
from pymongo.errors import ConnectionFailure

async def quick_check():
    """Quick database check"""
    print("🔍 Quick Database Check")
    print("-" * 30)
    
    try:
        # Test connection
        print("Testing connection...", end=" ")
        success = await db_config.connect()
        if not success:
            print("❌ FAILED")
            return False
        print("✅ OK")
        
        # Get database
        db = await get_db()
        
        # Check collections
        print("Checking collections...", end=" ")
        collections = await db.list_collection_names()
        print(f"✅ Found {len(collections)} collections")
        
        # Check each expected collection
        for name, collection_name in COLLECTIONS.items():
            collection = await get_collection(collection_name)
            count = await collection.count_documents({})
            print(f"  📋 {collection_name}: {count} documents")
        
        # Test a simple query
        print("Testing queries...", end=" ")
        users_collection = await get_collection(COLLECTIONS['users'])
        user_count = await users_collection.count_documents({})
        print(f"✅ OK ({user_count} users)")
        
        print("\n🎉 Database is working properly!")
        return True
        
    except ConnectionFailure:
        print("❌ Cannot connect to MongoDB!")
        print("Make sure MongoDB is running: sudo systemctl start mongod")
        return False
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

if __name__ == "__main__":
    try:
        success = asyncio.run(quick_check())
        if not success:
            sys.exit(1)
    except KeyboardInterrupt:
        print("\n⚠️  Check interrupted")
        sys.exit(1)
