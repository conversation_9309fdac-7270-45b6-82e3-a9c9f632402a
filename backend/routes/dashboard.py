"""
Dashboard routes for analytics and system management
"""
from datetime import datetime, timedelta
from typing import List, Dict, Any, Optional
from fastapi import APIRouter, HTTPException, Depends, status, Query
from pydantic import BaseModel

from models.message import MessageService
from models.booking import BookingService
from models.user import UserService
from config.database import get_collection, COLLECTIONS
from routes.auth import get_current_user

router = APIRouter()

# Response models
class DashboardStats(BaseModel):
    total_users: int
    total_messages: int
    total_bookings: int
    active_sessions: int
    today_bookings: int
    today_messages: int

class AnalyticsSummary(BaseModel):
    date_range: Dict[str, str]
    message_analytics: Dict[str, Any]
    booking_analytics: Dict[str, Any]
    user_analytics: Dict[str, Any]

class SystemMetrics(BaseModel):
    database_status: str
    collections_info: Dict[str, Any]
    performance_metrics: Dict[str, Any]

def get_services():
    """Get all service instances"""
    return {
        'message_service': MessageService(get_collection(COLLECTIONS['messages'])),
        'booking_service': BookingService(
            get_collection(COLLECTIONS['bookings']),
            get_collection(COLLECTIONS['time_slots'])
        ),
        'user_service': UserService(get_collection(COLLECTIONS['users']))
    }

# Routes
@router.get("/stats", response_model=DashboardStats)
async def get_dashboard_stats(current_user = Depends(get_current_user)):
    """Get overall dashboard statistics"""
    try:
        services = get_services()
        today = datetime.now().replace(hour=0, minute=0, second=0, microsecond=0)
        
        # Get collections
        users_collection = get_collection(COLLECTIONS['users'])
        messages_collection = get_collection(COLLECTIONS['messages'])
        bookings_collection = get_collection(COLLECTIONS['bookings'])
        
        # Count totals
        total_users = users_collection.count_documents({})
        total_messages = messages_collection.count_documents({})
        total_bookings = bookings_collection.count_documents({})
        
        # Count today's data
        today_messages = messages_collection.count_documents({
            "timestamp": {"$gte": today}
        })
        
        today_bookings = bookings_collection.count_documents({
            "created_at": {"$gte": today}
        })
        
        # Count active sessions (sessions with messages in last 24 hours)
        yesterday = today - timedelta(days=1)
        active_sessions_pipeline = [
            {"$match": {"timestamp": {"$gte": yesterday}}},
            {"$group": {"_id": "$session_id"}},
            {"$count": "active_sessions"}
        ]
        
        active_sessions_result = list(messages_collection.aggregate(active_sessions_pipeline))
        active_sessions = active_sessions_result[0]["active_sessions"] if active_sessions_result else 0
        
        return DashboardStats(
            total_users=total_users,
            total_messages=total_messages,
            total_bookings=total_bookings,
            active_sessions=active_sessions,
            today_bookings=today_bookings,
            today_messages=today_messages
        )
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to retrieve dashboard stats: {str(e)}"
        )

@router.get("/analytics", response_model=AnalyticsSummary)
async def get_analytics_summary(
    days: int = Query(7, ge=1, le=30),
    current_user = Depends(get_current_user)
):
    """Get comprehensive analytics summary"""
    try:
        services = get_services()
        end_date = datetime.now()
        start_date = end_date - timedelta(days=days)
        
        # Get message analytics
        message_analytics = services['message_service'].get_analytics_summary(start_date, end_date)
        
        # Get booking analytics
        bookings_collection = get_collection(COLLECTIONS['bookings'])
        booking_pipeline = [
            {
                "$match": {
                    "created_at": {"$gte": start_date, "$lte": end_date}
                }
            },
            {
                "$group": {
                    "_id": None,
                    "total_bookings": {"$sum": 1},
                    "confirmed_bookings": {
                        "$sum": {"$cond": [{"$eq": ["$status", "confirmed"]}, 1, 0]}
                    },
                    "cancelled_bookings": {
                        "$sum": {"$cond": [{"$eq": ["$status", "cancelled"]}, 1, 0]}
                    },
                    "services": {"$addToSet": "$service_type"},
                    "avg_bookings_per_day": {"$avg": 1}
                }
            }
        ]
        
        booking_analytics_result = list(bookings_collection.aggregate(booking_pipeline))
        booking_analytics = booking_analytics_result[0] if booking_analytics_result else {}
        
        # Get user analytics
        users_collection = get_collection(COLLECTIONS['users'])
        user_analytics = {
            "new_users": users_collection.count_documents({
                "created_at": {"$gte": start_date, "$lte": end_date}
            }),
            "active_users": users_collection.count_documents({
                "last_login": {"$gte": start_date, "$lte": end_date}
            })
        }
        
        return AnalyticsSummary(
            date_range={
                "start": start_date.isoformat(),
                "end": end_date.isoformat(),
                "days": days
            },
            message_analytics=message_analytics,
            booking_analytics=booking_analytics,
            user_analytics=user_analytics
        )
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to retrieve analytics summary: {str(e)}"
        )

@router.get("/metrics", response_model=SystemMetrics)
async def get_system_metrics(current_user = Depends(get_current_user)):
    """Get system performance metrics"""
    try:
        from config.database import db_config
        
        # Test database connection
        try:
            db = db_config.get_database()
            db.command("ping")
            db_status = "connected"
        except:
            db_status = "disconnected"
        
        # Get collection information
        collections_info = {}
        if db_status == "connected":
            for collection_name in COLLECTIONS.values():
                collection = get_collection(collection_name)
                collections_info[collection_name] = {
                    "document_count": collection.count_documents({}),
                    "indexes": len(list(collection.list_indexes()))
                }
        
        # Performance metrics (basic)
        performance_metrics = {
            "database_response_time_ms": 0,  # Could implement actual timing
            "average_query_time_ms": 0,      # Could implement query profiling
            "memory_usage_mb": 0,            # Could implement memory monitoring
            "disk_usage_mb": 0               # Could implement disk monitoring
        }
        
        return SystemMetrics(
            database_status=db_status,
            collections_info=collections_info,
            performance_metrics=performance_metrics
        )
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to retrieve system metrics: {str(e)}"
        )

@router.get("/users")
async def get_users_overview(
    limit: int = Query(50, ge=1, le=100),
    current_user = Depends(get_current_user)
):
    """Get users overview for admin dashboard"""
    try:
        users_collection = get_collection(COLLECTIONS['users'])
        
        # Get recent users
        users = list(users_collection.find(
            {},
            {"hashed_password": 0}  # Exclude password hash
        ).sort("created_at", -1).limit(limit))
        
        # Format users
        formatted_users = []
        for user in users:
            formatted_users.append({
                "id": str(user["_id"]),
                "name": user["name"],
                "email": user["email"],
                "phone": user["phone"],
                "is_active": user["is_active"],
                "created_at": user["created_at"].isoformat(),
                "last_login": user["last_login"].isoformat() if user.get("last_login") else None
            })
        
        return {
            "users": formatted_users,
            "total": len(formatted_users),
            "limit": limit
        }
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to retrieve users overview: {str(e)}"
        )

@router.get("/bookings/overview")
async def get_bookings_overview(
    days: int = Query(7, ge=1, le=30),
    current_user = Depends(get_current_user)
):
    """Get bookings overview for admin dashboard"""
    try:
        bookings_collection = get_collection(COLLECTIONS['bookings'])
        end_date = datetime.now()
        start_date = end_date - timedelta(days=days)
        
        # Get recent bookings
        bookings = list(bookings_collection.find({
            "created_at": {"$gte": start_date, "$lte": end_date}
        }).sort("created_at", -1).limit(100))
        
        # Format bookings
        formatted_bookings = []
        for booking in bookings:
            formatted_bookings.append({
                "id": str(booking["_id"]),
                "booking_id": booking["booking_id"],
                "name": booking["name"],
                "email": booking["email"],
                "service_type": booking["service_type"],
                "date": booking["date"],
                "time": booking["time"],
                "status": booking["status"],
                "created_at": booking["created_at"].isoformat()
            })
        
        # Get booking statistics by day
        daily_stats_pipeline = [
            {
                "$match": {
                    "created_at": {"$gte": start_date, "$lte": end_date}
                }
            },
            {
                "$group": {
                    "_id": {
                        "$dateToString": {
                            "format": "%Y-%m-%d",
                            "date": "$created_at"
                        }
                    },
                    "count": {"$sum": 1},
                    "confirmed": {
                        "$sum": {"$cond": [{"$eq": ["$status", "confirmed"]}, 1, 0]}
                    },
                    "cancelled": {
                        "$sum": {"$cond": [{"$eq": ["$status", "cancelled"]}, 1, 0]}
                    }
                }
            },
            {"$sort": {"_id": 1}}
        ]
        
        daily_stats = list(bookings_collection.aggregate(daily_stats_pipeline))
        
        return {
            "bookings": formatted_bookings,
            "daily_stats": daily_stats,
            "total": len(formatted_bookings),
            "date_range": {
                "start": start_date.isoformat(),
                "end": end_date.isoformat(),
                "days": days
            }
        }
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to retrieve bookings overview: {str(e)}"
        )
