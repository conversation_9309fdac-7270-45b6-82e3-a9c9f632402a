"""
Authentication routes with session-based authentication
"""
from datetime import datetime, timed<PERSON><PERSON>
from typing import Optional
from fastapi import APIRouter, HTTPException, Depends, status, Request, Response
from fastapi.responses import JSONResponse
from pydantic import BaseModel, EmailStr
import os
import secrets
import hashlib

from models.user import UserService, UserCreate, UserResponse, UserUpdate
from config.database import get_collection, COLLECTIONS

router = APIRouter()

# Session settings
SESSION_EXPIRE_HOURS = 24
SESSION_COOKIE_NAME = "session_id"

# Request/Response models
class LoginRequest(BaseModel):
    email: EmailStr
    password: str

class LoginResponse(BaseModel):
    success: bool
    message: str
    user: Optional[UserResponse] = None

class RegisterRequest(UserCreate):
    pass

# Session storage (in production, use Redis or database)
active_sessions = {}

class SessionData(BaseModel):
    user_id: str
    email: str
    created_at: datetime
    last_accessed: datetime

# Utility functions
def create_session_id() -> str:
    """Create a secure session ID"""
    return secrets.token_urlsafe(32)

def hash_session_id(session_id: str) -> str:
    """Hash session ID for storage"""
    return hashlib.sha256(session_id.encode()).hexdigest()

async def get_user_service():
    """Get user service instance"""
    users_collection = await get_collection(COLLECTIONS['users'])
    return UserService(users_collection)

def create_session(user_id: str, email: str) -> str:
    """Create a new session"""
    session_id = create_session_id()
    hashed_session = hash_session_id(session_id)

    session_data = SessionData(
        user_id=user_id,
        email=email,
        created_at=datetime.utcnow(),
        last_accessed=datetime.utcnow()
    )

    active_sessions[hashed_session] = session_data
    return session_id

def get_session(session_id: str) -> Optional[SessionData]:
    """Get session data"""
    if not session_id:
        return None

    hashed_session = hash_session_id(session_id)
    session_data = active_sessions.get(hashed_session)

    if not session_data:
        return None

    # Check if session is expired
    if datetime.utcnow() - session_data.created_at > timedelta(hours=SESSION_EXPIRE_HOURS):
        del active_sessions[hashed_session]
        return None

    # Update last accessed time
    session_data.last_accessed = datetime.utcnow()
    active_sessions[hashed_session] = session_data

    return session_data

def delete_session(session_id: str):
    """Delete a session"""
    if session_id:
        hashed_session = hash_session_id(session_id)
        active_sessions.pop(hashed_session, None)

async def get_current_user(request: Request):
    """Get current authenticated user from session"""
    session_id = request.cookies.get(SESSION_COOKIE_NAME)

    if not session_id:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Not authenticated"
        )

    session_data = get_session(session_id)
    if not session_data:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Invalid or expired session"
        )

    user_service = await get_user_service()
    user = await user_service.get_user_by_id(session_data.user_id)
    if not user:
        delete_session(session_id)
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="User not found"
        )

    return user

# Routes
@router.post("/register", response_model=LoginResponse)
async def register(user_data: RegisterRequest, response: Response):
    """Register a new user"""
    try:
        user_service = await get_user_service()
        user = await user_service.create_user(user_data)

        # Create session
        session_id = create_session(str(user.id), user.email)

        # Set session cookie
        response.set_cookie(
            key=SESSION_COOKIE_NAME,
            value=session_id,
            max_age=SESSION_EXPIRE_HOURS * 3600,  # Convert hours to seconds
            httponly=True,
            secure=False,  # Set to True in production with HTTPS
            samesite="lax"
        )

        # Convert to response model
        user_response = UserResponse(
            id=str(user.id),
            name=user.name,
            email=user.email,
            phone=user.phone,
            is_active=user.is_active,
            created_at=user.created_at,
            last_login=user.last_login,
            preferences=user.preferences
        )

        return LoginResponse(
            success=True,
            message="Registration successful",
            user=user_response
        )

    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Registration failed: {str(e)}"
        )

@router.post("/login", response_model=LoginResponse)
async def login(login_data: LoginRequest, response: Response):
    """Login user"""
    try:
        user_service = await get_user_service()
        user = await user_service.authenticate_user(login_data.email, login_data.password)

        if not user:
            return LoginResponse(
                success=False,
                message="Incorrect email or password"
            )

        # Create session
        session_id = create_session(str(user.id), user.email)

        # Set session cookie
        response.set_cookie(
            key=SESSION_COOKIE_NAME,
            value=session_id,
            max_age=SESSION_EXPIRE_HOURS * 3600,  # Convert hours to seconds
            httponly=True,
            secure=False,  # Set to True in production with HTTPS
            samesite="lax"
        )

        # Convert to response model
        user_response = UserResponse(
            id=str(user.id),
            name=user.name,
            email=user.email,
            phone=user.phone,
            is_active=user.is_active,
            created_at=user.created_at,
            last_login=user.last_login,
            preferences=user.preferences
        )

        return LoginResponse(
            success=True,
            message="Login successful",
            user=user_response
        )

    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Login failed: {str(e)}"
        )

@router.post("/logout")
async def logout(request: Request, response: Response):
    """Logout user"""
    session_id = request.cookies.get(SESSION_COOKIE_NAME)

    if session_id:
        delete_session(session_id)

    # Clear session cookie
    response.delete_cookie(key=SESSION_COOKIE_NAME)

    return {"success": True, "message": "Logged out successfully"}

@router.get("/me", response_model=UserResponse)
async def get_current_user_info(current_user = Depends(get_current_user)):
    """Get current user information"""
    return UserResponse(
        id=str(current_user.id),
        name=current_user.name,
        email=current_user.email,
        phone=current_user.phone,
        is_active=current_user.is_active,
        created_at=current_user.created_at,
        last_login=current_user.last_login,
        preferences=current_user.preferences
    )

@router.get("/check")
async def check_auth(request: Request):
    """Check if user is authenticated"""
    try:
        user = await get_current_user(request)
        return {
            "authenticated": True,
            "user": UserResponse(
                id=str(user.id),
                name=user.name,
                email=user.email,
                phone=user.phone,
                is_active=user.is_active,
                created_at=user.created_at,
                last_login=user.last_login,
                preferences=user.preferences
            )
        }
    except HTTPException:
        return {"authenticated": False}

@router.put("/me", response_model=UserResponse)
async def update_current_user(
    update_data: UserUpdate,
    current_user = Depends(get_current_user)
):
    """Update current user information"""
    try:
        user_service = await get_user_service()
        updated_user = await user_service.update_user(str(current_user.id), update_data)

        if not updated_user:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Failed to update user"
            )

        return UserResponse(
            id=str(updated_user.id),
            name=updated_user.name,
            email=updated_user.email,
            phone=updated_user.phone,
            is_active=updated_user.is_active,
            created_at=updated_user.created_at,
            last_login=updated_user.last_login,
            preferences=updated_user.preferences
        )

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Update failed: {str(e)}"
        )
